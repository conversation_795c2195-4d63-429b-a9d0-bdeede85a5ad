// Form Wizard Configuration
// This file defines the structure and flow of the job posting form

export const FORM_CATEGORIES = {
  social_media: {
    name: "Social Media",
    steps: ["social_media_platform", "social_media_goal"],
  },
  newsletter: {
    name: "Newsletter",
    steps: ["newsletter_goal", "newsletter_frequency", "newsletter_length"],
  },
  lead_magnet: {
    name: "Lead Magnet",
    steps: ["lead_magnet_goal"],
  },
};

export const STEP_DEFINITIONS = {
  // Initial category selection
  category_selection: {
    name: "Category Selection",
    title: "Tell us about the ghostwriter you need",
    required: true,
    validation: {
      fields: ["job_category"],
    },
  },

  // Topics selection step
  topics_selection: {
    name: "Topics Selection",
    title: "What topics should they write about?",
    required: false,
    validation: {
      fields: ["topics"],
    },
  },

  // Social Media Steps
  social_media_platform: {
    name: "Social Media Platform",
    title: "Social Media Platform",
    category: "social_media",
    required: true,
    validation: {
      fields: ["platform"],
    },
  },
  social_media_goal: {
    name: "Social Media Goal",
    title: "Social Media Goal",
    category: "social_media",
    required: true,
    validation: {
      fields: ["outcome"],
      conditional: {
        field: "outcome",
        values: ["leads", "booked_calls"],
        requiredFields: [
          "social_media_goal_type",
          "social_media_understands_risk_acknowledged",
        ],
      },
    },
  },

  // Newsletter Steps
  newsletter_goal: {
    name: "Newsletter Goal",
    title: "Newsletter Goal",
    category: "newsletter",
    required: true,
    validation: {
      fields: ["outcome"],
    },
  },
  newsletter_frequency: {
    name: "Newsletter Frequency",
    title: "Newsletter Frequency",
    category: "newsletter",
    required: true,
    validation: {
      fields: ["newsletter_frequency"],
    },
  },
  newsletter_length: {
    name: "Newsletter Length",
    title: "Newsletter Length",
    category: "newsletter",
    required: true,
    validation: {
      fields: ["newsletter_length"],
    },
  },

  // Lead Magnet Steps
  lead_magnet_goal: {
    name: "Lead Magnet Goal",
    title: "Lead Magnet Goal",
    category: "lead_magnet",
    required: true,
    validation: {
      fields: ["outcome"],
    },
  },

  // Common Steps
  job_details: {
    name: "Job Details",
    title: "Job Listing Details",
    required: true,
    validation: {
      fields: ["title", "description"],
    },
  },
  personal_brands: {
    name: "Personal Brands",
    title: "Personal Brands to Emulate",
    required: false,
    validation: {
      fields: [],
    },
  },
  topics: {
    name: "Topics",
    title: "Topics & Expertise",
    required: false,
    validation: {
      fields: ["topics"],
    },
  },
  budget: {
    name: "Budget",
    title: "Budget Range",
    required: true,
    validation: {
      fields: ["budget_range"],
    },
  },
  work_duration: {
    name: "Work Duration",
    title: "Work Duration",
    required: true,
    validation: {
      fields: ["work_duration"],
    },
  },
  client_info: {
    name: "Client Info",
    title: "Additional Information",
    required: false,
    validation: {
      fields: [],
    },
  },
};

export class FormConfig {
  constructor(selectedCategory = null) {
    this.selectedCategory = selectedCategory;
  }

  getStepSequence() {
    const sequence = ["category_selection"];

    // Add topics selection step after category
    sequence.push("topics_selection");

    if (this.selectedCategory && FORM_CATEGORIES[this.selectedCategory]) {
      const categorySteps = FORM_CATEGORIES[this.selectedCategory].steps;
      categorySteps.forEach((step) => {
        sequence.push(step);
      });
    }

    // Add common steps
    sequence.push(
      "job_details",
      "personal_brands",
      "budget",
      "work_duration",
      "client_info"
    );

    return sequence;
  }

  getStepDefinition(stepKey) {
    return STEP_DEFINITIONS[stepKey];
  }

  getStepByIndex(index) {
    const sequence = this.getStepSequence();
    const stepKey = sequence[index];
    return stepKey ? this.getStepDefinition(stepKey) : null;
  }

  getCurrentStepKey(currentIndex) {
    const sequence = this.getStepSequence();
    return sequence[currentIndex];
  }

  getTotalSteps() {
    return this.getStepSequence().length;
  }

  isLastStep(currentIndex) {
    return currentIndex >= this.getTotalSteps() - 1;
  }

  isFirstStep(currentIndex) {
    return currentIndex <= 0;
  }

  setCategory(category) {
    this.selectedCategory = category;
  }

  getProgressPercentage(currentIndex) {
    const total = this.getTotalSteps();
    return Math.round(((currentIndex + 1) / total) * 100);
  }
}
